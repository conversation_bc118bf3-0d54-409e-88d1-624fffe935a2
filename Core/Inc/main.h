//
// Created by <PERSON><PERSON><PERSON> on 2025/6/18.
//

#ifndef Q800_GD32_MAIN_H
#define Q800_GD32_MAIN_H

#include "gd32h7xx.h"
#include "systick.h"

#define RCU_GPIO_LCD_SPI_SDO    RCU_GPIOG
#define RCU_GPIO_LCD_SPI_SCL    RCU_GPIOD
// #define RCU_LCD_SPI             RCU_SPI0

#define TEST_1_PORT             GPIOD
#define TEST_1_PIN              GPIO_PIN_4


#define LCD_SPI_GPIO_AF         GPIO_AF_5

#define LCD_SPIX                SPI0

extern __IO uint32_t send_n;

#endif //Q800_GD32_MAIN_H
