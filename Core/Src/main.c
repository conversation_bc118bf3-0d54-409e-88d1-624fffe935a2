//
// Created by <PERSON><PERSON><PERSON> on 2025/6/18.
//

#include "main.h"
#include "my_main.h"
#include "lcd_rgb.h"
#include "i2c.h"
#include "i2c_bat.h"

/*!
    \brief      enable the CPU Chache
    \param[in]  none
    \param[out] none
    \retval     none
*/
void cache_enable() {
    /* Enable I-Cache */
    SCB_EnableICache();

    /* Enable D-Cache */
    SCB_EnableDCache();
}

/*!
    \brief      configure different peripheral clocks
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rcu_config() {
    /* enable the peripherals clock */
    // rcu_periph_clock_enable(RCU_GPIOA);
    // rcu_periph_clock_enable(RCU_GPIOD);
    // rcu_periph_clock_enable(RCU_GPIOF);
    // rcu_periph_clock_enable(RCU_GPIOG);
    // rcu_periph_clock_enable(RCU_GPIOE);

    rcu_periph_clock_enable(RCU_SPI0);
    // rcu_periph_clock_enable(RCU_DMA0);
    // rcu_periph_clock_enable(RCU_DMAMUX);
    rcu_spi_clock_config(IDX_SPI0, RCU_SPISRC_PLL0Q);
}

/*!
    \brief      configure the GPIO peripheral
    \param[in]  none
    \param[out] none
    \retval     none
*/
// void i2c_gpio_config() {
//     /* connect port to SPI0_NSS->PG10
//                        SPI0_SCK->PG11
//                        SPI0_MISO(SDI/SDA)->PG9
//                        SPI0_MOSI(SDO)->PD7 */
//     gpio_mode_set(GPIOG, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_10);
//     gpio_output_options_set(GPIOG, GPIO_OTYPE_PP, GPIO_OSPEED_60MHZ, GPIO_PIN_10);
//
//     gpio_af_set(GPIOG, GPIO_AF_5, GPIO_PIN_11);
//     gpio_af_set(GPIOD, GPIO_AF_5, GPIO_PIN_7);
//     // gpio_af_set(GPIOG, GPIO_AF_5, GPIO_PIN_9);
//
//     gpio_mode_set(GPIOG, GPIO_MODE_AF, GPIO_PUPD_NONE, GPIO_PIN_9);
//     gpio_output_options_set(GPIOG, GPIO_OTYPE_PP, GPIO_OSPEED_60MHZ, GPIO_PIN_9);
//
//     /* configure LED1 GPIO pin */
//     gpio_mode_set(GPIOF, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_10);
//     gpio_output_options_set(GPIOF, GPIO_OTYPE_PP, GPIO_OSPEED_60MHZ, GPIO_PIN_10);
//     /* configure LED2 GPIO pin */
//     gpio_mode_set(GPIOA, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_6);
//     gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_60MHZ, GPIO_PIN_6);
//
//     gpio_mode_set(GPIOD, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_0);
//     gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_60MHZ, GPIO_PIN_0);
//
//     /* reset LED1 GPIO pin */
//     gpio_bit_reset(GPIOF, GPIO_PIN_10);
//     /* reset LED2 GPIO pin */
//     gpio_bit_reset(GPIOA, GPIO_PIN_6);
//     gpio_bit_set(GPIOD, GPIO_PIN_0);
// }

/*!
    \brief      configure the SPI peripheral
    \param[in]  none
    \param[out] none
    \retval     none
*/
void spi0_config() {
    spi_parameter_struct spi_init_struct;
    /* deinitialize SPI and the parameters */
    spi_i2s_deinit(LCD_SPIX);
    spi_struct_para_init(&spi_init_struct);

    /* SPI0 parameter configuration */
    spi_init_struct.trans_mode = SPI_TRANSMODE_FULLDUPLEX;
    spi_init_struct.device_mode = SPI_MASTER;
    spi_init_struct.data_size = SPI_DATASIZE_8BIT;
    spi_init_struct.clock_polarity_phase = SPI_CK_PL_LOW_PH_1EDGE;
    spi_init_struct.nss = SPI_NSS_SOFT;
    spi_init_struct.prescale = SPI_PSC_32;
    spi_init_struct.endian = SPI_ENDIAN_MSB;
    spi_init(LCD_SPIX, &spi_init_struct);

    /* enable SPI byte access */
    spi_byte_access_enable(LCD_SPIX);

    spi_nss_internal_low(LCD_SPIX);
    spi_nss_output_enable(LCD_SPIX);
}

void nvic_config() {
    /* NVIC config */
    // nvic_priority_group_set(NVIC_PRIGROUP_PRE4_SUB0);
    // nvic_irq_enable(SPI0_IRQn, 1, 0);
}

int main() {
    /* enable the CPU Cache */
    cache_enable();
    /* configure systick */
    systick_config();
    /* NVIC config */
    nvic_config();
    /* peripheral clock enable */
    rcu_config();
    /* configure GPIO */
    // i2c_gpio_config();
    rcu_periph_clock_enable(RCU_GPIOD);
    gpio_mode_set(TEST_1_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, TEST_1_PIN);
    gpio_output_options_set(TEST_1_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_60MHZ, TEST_1_PIN);
    gpio_bit_reset(TEST_1_PORT, TEST_1_PIN);
    lcd_gpio_init();
    /* configure SPI0 */
    // spi_current_data_num_config(SPI0, 10);
    spi0_config();
    /* SPI enable */
    spi_enable(SPI0);
    /* SPI/I2S master start transfer */
    spi_master_transfer_start(SPI0, SPI_TRANS_START);
    /* SPI int enable */
    // spi_i2s_interrupt_enable(SPI0, SPI_I2S_INT_TP);
    /* configure GPIO */
    // i2c_gpio_config();

    /* configure I2C */
    // i2c_config();
    lcd_init();
    while (1) {
        // gpio_bit_set(TEST_1_PORT, TEST_1_PIN);
        // GD_Delay_ms(100);
        // gpio_bit_reset(TEST_1_PORT, TEST_1_PIN);
        // GD_Delay_ms(100);
        // gpio_test();
        // i2c_tra();
        // lcd_write_reg(0x00, 0x00);
    }
}