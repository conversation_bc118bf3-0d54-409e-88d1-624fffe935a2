<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Q800</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6230000::V6.23::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>GD32H757ZM</Device>
          <Vendor>GigaDevice</Vendor>
          <PackID>GigaDevice.GD32H7xx_DFP.1.4.0</PackID>
          <PackURL>https://gd32mcu.com</PackURL>
          <Cpu>IRAM(0x24000000,0x000D0000) IRAM2(0x00000000,0x00010000) IROM(0x08000000,0x03C0000) XRAM(0x20000000,0x00020000) XRAM2(0x30000000,0x00004000) XRAM3(0x30004000,0x00004000) CPUTYPE("Cortex-M7") FPU3(DFPU) CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC2000 -FN1 -FF0GD32H7xx_3840KB -********** -FL03C0000 -FP0($$Device:GD32H757ZM$Flash\GD32H7xx_3840KB.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:GD32H757ZM$Device\Include\gd32h7xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:GD32H757ZM$SVD\GD32H7xx.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>Q800\</OutputDirectory>
          <OutputName>Q800</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM7</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM7</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>-1</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M7"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>3</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x24000000</StartAddress>
                <Size>0xd0000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x3c0000</Size>
              </IROM>
              <XRAM>
                <Type>1</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x3c0000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x30000000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x30004000</StartAddress>
                <Size>0x4000</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x24000000</StartAddress>
                <Size>0xd0000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>4</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>1</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_STDPERIPH_DRIVER,GD32H7XX</Define>
              <Undefine></Undefine>
              <IncludePath>..\Core\Inc;..\Drivers\CMSIS\GD\GD32H7xx\Include;..\Drivers\GD32H7xx_standard_peripheral\Include;..\User\Hardware\Inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x24000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Application/MDK-ARM</GroupName>
          <Files>
            <File>
              <FileName>startup_gd32h7xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Drivers\CMSIS\Device\GD\GD32H7xx\Source\ARM\startup_gd32h7xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Application/User/Core</GroupName>
          <Files>
            <File>
              <FileName>gd32h7xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Core\Src\gd32h7xx_it.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Core\Src\main.c</FilePath>
            </File>
            <File>
              <FileName>systick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Core\Src\systick.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_gd32h7xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\CMSIS\Device\GD\GD32H7xx\Source\system_gd32h7xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers/GD32H7xx_Driver</GroupName>
          <Files>
            <File>
              <FileName>gd32h7xx_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_adc.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_can.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_cau.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_cau_aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau_aes.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_cau_des.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau_des.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_cau_tdes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_cau_tdes.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_cmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_cmp.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_cpdm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_cpdm.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_crc.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_ctc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_ctc.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_dac.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_dbg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_dbg.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_dci.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_dci.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_dma.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_edout.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_edout.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_efuse.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_efuse.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_enet.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_enet.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_exmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_exmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_exti.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_fac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_fac.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_fmc.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_fwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_fwdgt.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_gpio.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_hau.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_hau.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_hau_sha_md5.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_hau_sha_md5.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_hpdf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_hpdf.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_hwsem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_hwsem.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_i2c.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_ipa.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_ipa.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_lpdts.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_lpdts.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_mdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_mdio.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_mdma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_mdma.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_misc.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_ospi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_ospi.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_ospim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_ospim.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_pmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_pmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_rameccmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_rameccmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_rcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_rcu.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_rspdif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_rspdif.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_rtc.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_rtdec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_rtdec.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_sai.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_sai.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_sdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_sdio.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_spi.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_timer.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_tli.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_tli.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_tmu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_tmu.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_trigsel.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_trigsel.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_trng.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_usart.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_vref.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_vref.c</FilePath>
            </File>
            <File>
              <FileName>gd32h7xx_wwdgt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Drivers\GD32H7xx_standard_peripheral\Source\gd32h7xx_wwdgt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User/Hardware</GroupName>
          <Files>
            <File>
              <FileName>lcd_rgb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\lcd_rgb.c</FilePath>
            </File>
            <File>
              <FileName>my_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\my_main.c</FilePath>
            </File>
            <File>
              <FileName>system.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\system.c</FilePath>
            </File>
            <File>
              <FileName>delay_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\delay_tim.c</FilePath>
            </File>
            <File>
              <FileName>i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\i2c.c</FilePath>
            </File>
            <File>
              <FileName>i2c_bat.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\i2c_bat.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\spi.c</FilePath>
            </File>
            <File>
              <FileName>picture.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\picture.c</FilePath>
            </File>
            <File>
              <FileName>picture2.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\User\Hardware\Src\picture2.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="6.1.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.7.36" url="https://www.keil.com/pack/" vendor="ARM" version="6.1.0"/>
        <targetInfos>
          <targetInfo name="Q800"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>Q800</LayName>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
