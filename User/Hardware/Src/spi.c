//
// Created by <PERSON><PERSON> on 2025/7/25.
//

#include "spi.h"
#include "lcd_rgb.h"

#define DEFAULT_SPI_TIMEOUT 10000

void SPI_Transmit(uint32_t spi_periph, const uint8_t *pTxData, uint8_t *pRxData, uint16_t Size) {
    gpio_bit_reset(LCD_SPI_SCS_PORT, LCD_SPI_SCS_PIN);
    uint16_t flag_timeout = DEFAULT_SPI_TIMEOUT;

    while (spi_i2s_flag_get(spi_periph, SPI_FLAG_TP) == RESET) {
        if (flag_timeout) {
            flag_timeout--;
        } else {
            break;
        }
    }

    for (int i = 0; i < Size; ++i) {
        spi_i2s_data_transmit(spi_periph, *pTxData++);
        // SPI_FLAG_TC
        // TxFIFO传输完成标志
        // 0：有数据保存在TxFIFO中，或者TxFIFO正进行最后一帧数据的传输（包含CRC）
        // 1：最后一个数据帧或CRC帧已发送结束
        if (i == Size - 1) {
            uint16_t timeout = DEFAULT_SPI_TIMEOUT;
            while (spi_i2s_flag_get(spi_periph, SPI_FLAG_TC) == RESET) {
                if (timeout) {
                    timeout--;
                } else {
                    break;
                }
            }
        }
    }
    gpio_bit_set(LCD_SPI_SCS_PORT, LCD_SPI_SCS_PIN);
}
