//
// Created by <PERSON><PERSON> on 2025/6/23.
//

#include "system.h"
// #include "lvgl.h"
// #include "data_storage.h"
#if 0

#define TASK_NUM_MAX 3
#define LED_RED_WriteT HAL_GPIO_TogglePin(LED_RED_GPIO_Port, LED_RED_Pin)
#define IGBT_EN_WriteT HAL_GPIO_TogglePin(IGBT_EN_GPIO_Port, IGBT_EN_Pin)

void delay_us(uint32_t count) {
    // 左移6位即乘以 2^6=64，此时与TIM6的Prescaler对应
    // Prescaler = 1时，即主频/2，=0时即主频/1
    // count <<= 6;
    // 传入的值不可大于 65536/64 = 1,024，不然会溢出
    __HAL_TIM_SET_COUNTER(&htim6, 65535 - count);
    __HAL_TIM_CLEAR_FLAG(&htim6, TIM_FLAG_UPDATE);

    HAL_TIM_Base_Start(&htim6);
    // 避免CEN被意外关闭，导致定时器CNT不继续计数，添加CEN状态判断条件
    while (__HAL_TIM_GET_FLAG(&htim6, TIM_FLAG_UPDATE) == RESET && (TIM6->CR1 & TIM_CR1_CEN));
    HAL_TIM_Base_Stop(&htim6);
}

void delay_ms(uint16_t count) {
    while (count--) {
        delay_us(1000);
    }
}

void led_handle() {
    LED_RED_WriteT;
    // IGBT_EN_WriteT;
}

void lvgl_task() {
    lv_task_handler();
}

static TaskComps_t TaskComps[TASK_NUM_MAX] = {
        {0, 1, 1, lvgl_task},
        {0, 100, 100, led_handle},
        {0, 1000, 1000, storage_save_data},
};

void task_schedule() {
    for (uint8_t i = 0; i < TASK_NUM_MAX; ++i) {
        if (TaskComps[i].tim_count) {
            TaskComps[i].tim_count--;
            if (TaskComps[i].tim_count == 0) {
                TaskComps[i].tim_count = TaskComps[i].tim_reload;
                TaskComps[i].run = 1;
            }
        }
    }
}

void HAL_IncTick(void) {
    lv_tick_inc(1);
    task_schedule();
    uwTick += (uint32_t) uwTickFreq;
}

void task_handler() {
    for (uint8_t i = 0; i < TASK_NUM_MAX; ++i) {
        if (TaskComps[i].run) {
            TaskComps[i].run = 0;
            TaskComps[i].p_task_func();
        }
    }
}
#endif