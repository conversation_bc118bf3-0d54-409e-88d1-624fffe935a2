//
// Created by <PERSON><PERSON> on 2025/6/23.
//

#include "delay_tim.h"

#define DELAY_TIMER_RCU_PERIPH  RCU_TIMER6
#define DELAY_TIMER_PERIPH      TIMER6
#define DELAY_TIMER_NVIC_IRQ    TIMER6_IRQn
#define DELAY_TIMER_IRQ_HANDLER TIMER6_IRQHandler

//int_freq = CLK / ((prescaler + 1) * period)
//int_freq = 1us
// #define DELAY_TIMER_PRESCALER   1
// #define DELAY_TIMER_PERIOD      60

#define DELAY_TIMER_PRESCALER   (600-1)
#define DELAY_TIMER_PERIOD      65535

volatile static uint32_t gs_count = 0;

void delay_timer_init() {
    timer_parameter_struct timer_parameter;

    rcu_periph_clock_enable(DELAY_TIMER_RCU_PERIPH);
    // 预分频
    timer_parameter.prescaler = DELAY_TIMER_PRESCALER;
    // 对齐模式
    timer_parameter.alignedmode = TIMER_COUNTER_EDGE;
    // 定时器增长方向
    timer_parameter.counterdirection = TIMER_COUNTER_UP;
    // 定时器自动加载值
    timer_parameter.period = DELAY_TIMER_PERIOD;
    // 时钟分频值
    timer_parameter.clockdivision = TIMER_CKDIV_DIV1;

    timer_init(DELAY_TIMER_PERIPH, &timer_parameter);
    timer_interrupt_enable(DELAY_TIMER_PERIPH, TIMER_INT_UP);
    nvic_irq_enable(DELAY_TIMER_NVIC_IRQ, 0, 0);
    timer_enable(DELAY_TIMER_PERIPH);
}

void DELAY_TIMER_IRQ_HANDLER() {
    timer_interrupt_flag_clear(DELAY_TIMER_PERIPH, TIMER_INT_UP);

    gs_count++;
}

/**
 * @brief 阻塞延时定时器初始化
 */
void delay_tim_init() {
    delay_timer_init();
}

/**
 * @brief delay a time in microsecond
 * @param us [in] us
 */
void delay_us(uint32_t us) {
    uint32_t start_count = gs_count;
    while ((gs_count - start_count) < us);
}

/**
 * @brief delay a time in milliseconds
 * @param ms [in] ms
 */
void delay_ms(uint32_t ms) {
    delay_us(ms * 1000);
}