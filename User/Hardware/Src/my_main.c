//
// Created by <PERSON><PERSON> on 2025/6/23.
//

#include "my_main.h"

void gpio_test() {
    /* turn on LED1, turn off LED2 */
    gpio_bit_set(GPIOF, GPIO_PIN_10);
    gpio_bit_reset(GPIOA, GPIO_PIN_6);

    GD_Delay_ms(500);

    /* turn on LED2 and LED1 */
    gpio_bit_set(GPIOA, GPIO_PIN_6);
    gpio_bit_set(GPIOF, GPIO_PIN_10);
    GD_Delay_ms(500);

    /* turn off LED1 and LED2 */
    gpio_bit_reset(GPIOA, GPIO_PIN_6);
    gpio_bit_reset(GPIOF, GPIO_PIN_10);
    GD_Delay_ms(500);
}