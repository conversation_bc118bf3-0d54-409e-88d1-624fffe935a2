//
// Created by <PERSON><PERSON> on 2025/7/8.
//

#include "i2c_bat.h"
#include "i2c.h"


uint8_t i2c_transmitter[16];

void i2c_tra() {
    for (uint8_t i = 0; i < 16; i++) {
        i2c_transmitter[i] = i + 0x80;
    }

    /* wait until I2C bus is idle */
    while (i2c_flag_get(I2CX, I2C_FLAG_I2CBSY));
    /* send a start condition to I2C bus */
    i2c_start_on_bus(I2CX);
    /* wait until the transmit data buffer is empty */
    I2C_STAT(I2CX) |= I2C_STAT_TBE;
    while (!i2c_flag_get(I2CX, I2C_FLAG_TBE));

    for (uint8_t i = 0; i < 16; i++) {
        /* data transmission */
        i2c_data_transmit(I2CX, i2c_transmitter[i]);
        /* wait until the TI bit is set */
        while (!i2c_flag_get(I2CX, I2C_FLAG_TI));
    }
    /* wait for transfer complete flag */
    while (!i2c_flag_get(I2CX, I2C_FLAG_TC));
    /* send a stop condition to I2C bus */
    i2c_stop_on_bus(I2CX);
    /* wait until stop condition generate */
    while (!i2c_flag_get(I2CX, I2C_FLAG_STPDET));
    /* clear the STPDET bit */
    i2c_flag_clear(I2CX, I2C_FLAG_STPDET);
}
