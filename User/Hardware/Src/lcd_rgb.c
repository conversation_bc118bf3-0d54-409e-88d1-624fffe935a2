//
// Created by <PERSON><PERSON> on 2025/6/23.
//

#include "lcd_rgb.h"
#include "spi.h"
#include "picture.h"

#define HSPW 2
#define HBPD 44
#define HFPD 46
#define VSPW 2
#define VBPD 7
#define VFPD 16
#define HDP 640
#define VDP 480

#define PCLK 22

#define HORIZONTAL_SYNCHRONOUS_PULSE  2
#define HORIZONTAL_BACK_PORCH         44
#define ACTIVE_WIDTH                  640
#define HORIZONTAL_FRONT_PORCH        46

#define VERTICAL_SYNCHRONOUS_PULSE    2
#define VERTICAL_BACK_PORCH           7
#define ACTIVE_HEIGHT                 480
#define VERTICAL_FRONT_PORCH          16


#define LCD_FRAME_BUFFER             (0x24000000)    //第一层首地址
#define BUFFER_OFFSET                (640*480*2)     //一层液晶的数据量
#define LCD_PIXCELS                  (640*480)

/* pointer to the starting address of the framebuffer in memory */
uint16_t *framebuffer = (uint16_t *) LCD_FRAME_BUFFER;

void lcd_spi_gpio_config() {
    /* connect port to LCD_SCS->PG10
                       LCD_SCK->PG11
                       LCD_SDA->PG9
                       LCD_SDO->PD7 */
    gpio_mode_set(LCD_SPI_SCS_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, LCD_SPI_SCS_PIN);
    gpio_output_options_set(LCD_SPI_SCS_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_SPI_SCS_PIN);
    gpio_bit_set(LCD_SPI_SCS_PORT, LCD_SPI_SCS_PIN);

    gpio_mode_set(LCD_SPI_SCK_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, LCD_SPI_SCK_PIN);
    gpio_output_options_set(LCD_SPI_SCK_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_SPI_SCK_PIN);
    gpio_bit_set(LCD_SPI_SCK_PORT, LCD_SPI_SCK_PIN);

    gpio_mode_set(LCD_SPI_SDO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, LCD_SPI_SDO_PIN);
    gpio_output_options_set(LCD_SPI_SDO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_SPI_SDO_PIN);
    gpio_bit_reset(LCD_SPI_SDO_PORT, LCD_SPI_SDO_PIN);

    gpio_mode_set(LCD_SPI_SDA_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, LCD_SPI_SDA_PIN);
    gpio_output_options_set(LCD_SPI_SDA_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_SPI_SDA_PIN);
    gpio_bit_reset(LCD_SPI_SDA_PORT, LCD_SPI_SDA_PIN);

    gpio_mode_set(LCD_SPI_RES_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, LCD_SPI_RES_PIN);
    gpio_output_options_set(LCD_SPI_RES_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_SPI_RES_PIN);
    gpio_bit_set(LCD_SPI_RES_PORT, LCD_SPI_RES_PIN);
}

void lcd_write_reg(uint8_t reg, uint8_t data) {
    
}

/*!
    \brief      使用默认颜色初始化帧缓冲区
    \param[in]  none
    \param[out] none
    \retval     none
*/
void framebuffer_init(uint16_t color) {
    for (uint32_t y = 0; y < ACTIVE_HEIGHT; ++y) {
        for (uint32_t x = 0; x < ACTIVE_WIDTH; ++x) {
            framebuffer[y * ACTIVE_WIDTH + x] = color;
        }
    }
}

void lcd_gpio_init() {
    // PG14 - TLI_B0
    rcu_periph_clock_enable(LCD_B0_GPIO_CLK);
    gpio_af_set(LCD_B0_GPIO_PORT, LCD_B0_GPIO_AF, LCD_B0_GPIO_PIN);
    gpio_mode_set(LCD_B0_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B0_GPIO_PIN);
    gpio_output_options_set(LCD_B0_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B0_GPIO_PIN);

    // PG12 - TLI_B1
    rcu_periph_clock_enable(LCD_B1_GPIO_CLK);
    gpio_af_set(LCD_B1_GPIO_PORT, LCD_B1_GPIO_AF, LCD_B1_GPIO_PIN);
    gpio_mode_set(LCD_B1_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B1_GPIO_PIN);
    gpio_output_options_set(LCD_B1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B1_GPIO_PIN);

    // PC9 - TLI_B2
    rcu_periph_clock_enable(LCD_B2_GPIO_CLK);
    gpio_af_set(LCD_B2_GPIO_PORT, LCD_B2_GPIO_AF, LCD_B2_GPIO_PIN);
    gpio_mode_set(LCD_B2_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B2_GPIO_PIN);
    gpio_output_options_set(LCD_B2_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B2_GPIO_PIN);

    // PA8 - TLI_B3
    rcu_periph_clock_enable(LCD_B3_GPIO_CLK);
    gpio_af_set(LCD_B3_GPIO_PORT, LCD_B3_GPIO_AF, LCD_B3_GPIO_PIN);
    gpio_mode_set(LCD_B3_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B3_GPIO_PIN);
    gpio_output_options_set(LCD_B3_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B3_GPIO_PIN);

    // PC11 - TLI_B4
    rcu_periph_clock_enable(LCD_B4_GPIO_CLK);
    gpio_af_set(LCD_B4_GPIO_PORT, LCD_B4_GPIO_AF, LCD_B4_GPIO_PIN);
    gpio_mode_set(LCD_B4_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B4_GPIO_PIN);
    gpio_output_options_set(LCD_B4_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B4_GPIO_PIN);

    // PB5 - TLI_B5
    rcu_periph_clock_enable(LCD_B5_GPIO_CLK);
    gpio_af_set(LCD_B5_GPIO_PORT, LCD_B5_GPIO_AF, LCD_B5_GPIO_PIN);
    gpio_mode_set(LCD_B5_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B5_GPIO_PIN);
    gpio_output_options_set(LCD_B5_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B5_GPIO_PIN);

    // PB8 - TLI_B6
    rcu_periph_clock_enable(LCD_B6_GPIO_CLK);
    gpio_af_set(LCD_B6_GPIO_PORT, LCD_B6_GPIO_AF, LCD_B6_GPIO_PIN);
    gpio_mode_set(LCD_B6_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B6_GPIO_PIN);
    gpio_output_options_set(LCD_B6_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B6_GPIO_PIN);

    // PB9 - TLI_B7
    rcu_periph_clock_enable(LCD_B7_GPIO_CLK);
    gpio_af_set(LCD_B7_GPIO_PORT, LCD_B7_GPIO_AF, LCD_B7_GPIO_PIN);
    gpio_mode_set(LCD_B7_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_B7_GPIO_PIN);
    gpio_output_options_set(LCD_B7_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_B7_GPIO_PIN);

    // PE13 - TLI_DE
    rcu_periph_clock_enable(LCD_DE_GPIO_CLK);
    gpio_af_set(LCD_DE_GPIO_PORT, LCD_DE_GPIO_AF, LCD_DE_GPIO_PIN);
    gpio_mode_set(LCD_DE_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_DE_GPIO_PIN);
    gpio_output_options_set(LCD_DE_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_DE_GPIO_PIN);

    // PB1 - TLI_G0
    rcu_periph_clock_enable(LCD_G0_GPIO_CLK);
    gpio_af_set(LCD_G0_GPIO_PORT, LCD_G0_GPIO_AF, LCD_G0_GPIO_PIN);
    gpio_mode_set(LCD_G0_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G0_GPIO_PIN);
    gpio_output_options_set(LCD_G0_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G0_GPIO_PIN);

    // PB0 - TLI_G1
    rcu_periph_clock_enable(LCD_G1_GPIO_CLK);
    gpio_af_set(LCD_G1_GPIO_PORT, LCD_G1_GPIO_AF, LCD_G1_GPIO_PIN);
    gpio_mode_set(LCD_G1_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G1_GPIO_PIN);
    gpio_output_options_set(LCD_G1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G1_GPIO_PIN);

    // PA6 - TLI_G2
    rcu_periph_clock_enable(LCD_G2_GPIO_CLK);
    gpio_af_set(LCD_G2_GPIO_PORT, LCD_G2_GPIO_AF, LCD_G2_GPIO_PIN);
    gpio_mode_set(LCD_G2_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G2_GPIO_PIN);
    gpio_output_options_set(LCD_G2_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G2_GPIO_PIN);

    // PE11 - TLI_G3
    rcu_periph_clock_enable(LCD_G3_GPIO_CLK);
    gpio_af_set(LCD_G3_GPIO_PORT, LCD_G3_GPIO_AF, LCD_G3_GPIO_PIN);
    gpio_mode_set(LCD_G3_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G3_GPIO_PIN);
    gpio_output_options_set(LCD_G3_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G3_GPIO_PIN);

    // PB10 - TLI_G4
    rcu_periph_clock_enable(LCD_G4_GPIO_CLK);
    gpio_af_set(LCD_G4_GPIO_PORT, LCD_G4_GPIO_AF, LCD_G4_GPIO_PIN);
    gpio_mode_set(LCD_G4_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G4_GPIO_PIN);
    gpio_output_options_set(LCD_G4_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G4_GPIO_PIN);

    // PC1 - TLI_G5
    rcu_periph_clock_enable(LCD_G5_GPIO_CLK);
    gpio_af_set(LCD_G5_GPIO_PORT, LCD_G5_GPIO_AF, LCD_G5_GPIO_PIN);
    gpio_mode_set(LCD_G5_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G5_GPIO_PIN);
    gpio_output_options_set(LCD_G5_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G5_GPIO_PIN);

    // PC7 - TLI_G6
    rcu_periph_clock_enable(LCD_G6_GPIO_CLK);
    gpio_af_set(LCD_G6_GPIO_PORT, LCD_G6_GPIO_AF, LCD_G6_GPIO_PIN);
    gpio_mode_set(LCD_G6_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G6_GPIO_PIN);
    gpio_output_options_set(LCD_G6_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G6_GPIO_PIN);

    // PD3 - TLI_G7
    rcu_periph_clock_enable(LCD_G7_GPIO_CLK);
    gpio_af_set(LCD_G7_GPIO_PORT, LCD_G7_GPIO_AF, LCD_G7_GPIO_PIN);
    gpio_mode_set(LCD_G7_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_G7_GPIO_PIN);
    gpio_output_options_set(LCD_G7_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_G7_GPIO_PIN);

    // PE15 - TLI_HSYNC
    rcu_periph_clock_enable(LCD_H_GPIO_CLK);
    gpio_af_set(LCD_H_GPIO_PORT, LCD_H_GPIO_AF, LCD_H_GPIO_PIN);
    gpio_mode_set(LCD_H_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_H_GPIO_PIN);
    gpio_output_options_set(LCD_H_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_H_GPIO_PIN);

    // PE14 - TLI_PIXCLK
    rcu_periph_clock_enable(LCD_PCLK_GPIO_CLK);
    gpio_af_set(LCD_PCLK_GPIO_PORT, LCD_PCLK_GPIO_AF, LCD_PCLK_GPIO_PIN);
    gpio_mode_set(LCD_PCLK_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_PCLK_GPIO_PIN);
    gpio_output_options_set(LCD_PCLK_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_PCLK_GPIO_PIN);

    // PG13 - TLI_R0
    rcu_periph_clock_enable(LCD_R0_GPIO_CLK);
    gpio_af_set(LCD_R0_GPIO_PORT, LCD_R0_GPIO_AF, LCD_R0_GPIO_PIN);
    gpio_mode_set(LCD_R0_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R0_GPIO_PIN);
    gpio_output_options_set(LCD_R0_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R0_GPIO_PIN);

    // PA2 - TLI_R1
    rcu_periph_clock_enable(LCD_R1_GPIO_CLK);
    gpio_af_set(LCD_R1_GPIO_PORT, LCD_R1_GPIO_AF, LCD_R1_GPIO_PIN);
    gpio_mode_set(LCD_R1_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R1_GPIO_PIN);
    gpio_output_options_set(LCD_R1_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R1_GPIO_PIN);

    // PC10 - TLI_R2
    rcu_periph_clock_enable(LCD_R2_GPIO_CLK);
    gpio_af_set(LCD_R2_GPIO_PORT, LCD_R2_GPIO_AF, LCD_R2_GPIO_PIN);
    gpio_mode_set(LCD_R2_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R2_GPIO_PIN);
    gpio_output_options_set(LCD_R2_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R2_GPIO_PIN);

    // PA15 - TLI_R3
    rcu_periph_clock_enable(LCD_R3_GPIO_CLK);
    gpio_af_set(LCD_R3_GPIO_PORT, LCD_R3_GPIO_AF, LCD_R3_GPIO_PIN);
    gpio_mode_set(LCD_R3_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R3_GPIO_PIN);
    gpio_output_options_set(LCD_R3_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R3_GPIO_PIN);

    // PA5 - TLI_R4
    rcu_periph_clock_enable(LCD_R4_GPIO_CLK);
    gpio_af_set(LCD_R4_GPIO_PORT, LCD_R4_GPIO_AF, LCD_R4_GPIO_PIN);
    gpio_mode_set(LCD_R4_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R4_GPIO_PIN);
    gpio_output_options_set(LCD_R4_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R4_GPIO_PIN);

    // PC0 - TLI_R5
    rcu_periph_clock_enable(LCD_R5_GPIO_CLK);
    gpio_af_set(LCD_R5_GPIO_PORT, LCD_R5_GPIO_AF, LCD_R5_GPIO_PIN);
    gpio_mode_set(LCD_R5_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R5_GPIO_PIN);
    gpio_output_options_set(LCD_R5_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R5_GPIO_PIN);

    // PC12 - TLI_R6
    rcu_periph_clock_enable(LCD_R6_GPIO_CLK);
    gpio_af_set(LCD_R6_GPIO_PORT, LCD_R6_GPIO_AF, LCD_R6_GPIO_PIN);
    gpio_mode_set(LCD_R6_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R6_GPIO_PIN);
    gpio_output_options_set(LCD_R6_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R6_GPIO_PIN);

    // PC4 - TLI_R7
    rcu_periph_clock_enable(LCD_R7_GPIO_CLK);
    gpio_af_set(LCD_R7_GPIO_PORT, LCD_R7_GPIO_AF, LCD_R7_GPIO_PIN);
    gpio_mode_set(LCD_R7_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_R7_GPIO_PIN);
    gpio_output_options_set(LCD_R7_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_R7_GPIO_PIN);

    // PA4 - TLI_VSYNC
    rcu_periph_clock_enable(LCD_V_GPIO_CLK);
    gpio_af_set(LCD_V_GPIO_PORT, LCD_V_GPIO_AF, LCD_V_GPIO_PIN);
    gpio_mode_set(LCD_V_GPIO_PORT, GPIO_MODE_AF, GPIO_PUPD_NONE, LCD_V_GPIO_PIN);
    gpio_output_options_set(LCD_V_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_100_220MHZ, LCD_V_GPIO_PIN);

    // PE5 - LCD_BL
    rcu_periph_clock_enable(LCD_BL_GPIO_CLK);
    gpio_mode_set(LCD_BL_GPIO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, LCD_BL_GPIO_PIN);
    gpio_output_options_set(LCD_BL_GPIO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_60MHZ, LCD_BL_GPIO_PIN);
    gpio_bit_set(LCD_BL_GPIO_PORT, LCD_BL_GPIO_PIN);
}

void my_tli_init() {
    tli_parameter_struct tli_init_struct;

    rcu_periph_clock_enable(RCU_TLI);
    lcd_gpio_init();

    /* configure the pll2 input and output clock range */
    rcu_pll_input_output_clock_range_config(IDX_PLL2, RCU_PLL2RNG_1M_2M, RCU_PLL2VCO_150M_420M);
    /* configure the PLL2 clock: CK_PLL2P/CK_PLL2Q/CK_PLL2R = HXTAL_VALUE / 25 * 500 / 3 */
    if (ERROR == rcu_pll2_config(25, 176, 4, 4, 4)) {
        while (1);
    }
    /* enable PLL2R clock output */
    rcu_pll_clock_output_enable(RCU_PLL2R);
    rcu_tli_clock_div_config(RCU_PLL2R_DIV2);

    rcu_osci_on(RCU_PLL2_CK);

    if (ERROR == rcu_osci_stab_wait(RCU_PLL2_CK)) {
        while (1);
    }

    /* 配置TLI参数结构 */
    /*信号极性配置*/
    /* 行同步信号极性 */
    tli_init_struct.signalpolarity_hs = TLI_HSYN_ACTLIVE_LOW; //水平同步极性，设置低电平或是高电平有效
    /* 垂直同步信号极性 */
    tli_init_struct.signalpolarity_vs = TLI_VSYN_ACTLIVE_LOW; //垂直同步极性
    /* 数据使能信号极性 */
    tli_init_struct.signalpolarity_de = TLI_DE_ACTLIVE_LOW; //数据使能极性
    /* 像素同步时钟极性 */
    tli_init_struct.signalpolarity_pixelck = TLI_PIXEL_CLOCK_TLI; //像素时钟极性
    /* LCD显示定时配置 */
    tli_init_struct.synpsz_hpsz = HORIZONTAL_SYNCHRONOUS_PULSE - 1; //水平同步宽度
    tli_init_struct.synpsz_vpsz = VERTICAL_SYNCHRONOUS_PULSE - 1;   //垂直同步宽度
    tli_init_struct.backpsz_hbpsz =
            HORIZONTAL_SYNCHRONOUS_PULSE + HORIZONTAL_BACK_PORCH - 1; //水平同步后沿宽度
    tli_init_struct.backpsz_vbpsz =
            VERTICAL_SYNCHRONOUS_PULSE + VERTICAL_BACK_PORCH - 1;                                            //垂直同步后沿高度
    tli_init_struct.activesz_hasz = HORIZONTAL_SYNCHRONOUS_PULSE + HORIZONTAL_BACK_PORCH + ACTIVE_WIDTH - 1; //有效宽度
    tli_init_struct.activesz_vasz = VERTICAL_SYNCHRONOUS_PULSE + VERTICAL_BACK_PORCH + ACTIVE_HEIGHT - 1;    //有效高度
    tli_init_struct.totalsz_htsz =
            HORIZONTAL_SYNCHRONOUS_PULSE + HORIZONTAL_BACK_PORCH + ACTIVE_WIDTH + HORIZONTAL_FRONT_PORCH - 1; //总宽度
    tli_init_struct.totalsz_vtsz =
            VERTICAL_SYNCHRONOUS_PULSE + VERTICAL_BACK_PORCH + ACTIVE_HEIGHT + VERTICAL_FRONT_PORCH - 1; //总高度

    tli_init_struct.backcolor_red = 0xFF;   //屏幕背景层红色部分
    tli_init_struct.backcolor_green = 0x00; //屏幕背景层绿色部分
    tli_init_struct.backcolor_blue = 0x00;  //屏幕背景色蓝色部分

    tli_init(&tli_init_struct);
}

/**
  * @brief 初始化LTD的 层 参数
  *           - 设置显存空间
  *           - 设置分辨率
  * @param  None
  * @retval None
  */
void lcd_layer_init() {
    tli_layer_parameter_struct tli_layer_init_struct;

    /* TLI layer0配置 */
    /* TLI窗口大小配置 */
    // tli_layer_init_struct.layer_window_leftpos = 00 + 0 + lcd_param[cur_lcd].hsw + lcd_param[cur_lcd].hbp;
    // tli_layer_init_struct.layer_window_rightpos = (ACTIVE_WIDTH + lcd_param[cur_lcd].hsw + lcd_param[cur_lcd].hbp - 1);
    // tli_layer_init_struct.layer_window_toppos = 00 + 0 + lcd_param[cur_lcd].vsw + lcd_param[cur_lcd].vbp;
    // tli_layer_init_struct.layer_window_bottompos = (ACTIVE_HEIGHT + 0 + lcd_param[cur_lcd].vsw +
    //                                                 lcd_param[cur_lcd].vbp - 1);
    tli_layer_init_struct.layer_window_leftpos = 00 + 0 + HORIZONTAL_SYNCHRONOUS_PULSE + HORIZONTAL_BACK_PORCH;
    tli_layer_init_struct.layer_window_rightpos = (ACTIVE_WIDTH + HORIZONTAL_SYNCHRONOUS_PULSE +
                                                   HORIZONTAL_BACK_PORCH - 1);
    tli_layer_init_struct.layer_window_toppos = 00 + 0 + VERTICAL_SYNCHRONOUS_PULSE + VERTICAL_BACK_PORCH;
    tli_layer_init_struct.layer_window_bottompos = (ACTIVE_HEIGHT + 0 + VERTICAL_SYNCHRONOUS_PULSE +
                                                    VERTICAL_BACK_PORCH - 1);
    /* TLI窗口像素格式配置 */
    tli_layer_init_struct.layer_ppf = LAYER_PPF_RGB565; //TLI_LxPPF  PPF[2:0]  010：RGB565
    /* TLI窗口指定alpha配置 */
    tli_layer_init_struct.layer_sa = 0xff; //层透明度 255为完全不透明
    /* TLI层默认alpha R、G、B值配置 */
    tli_layer_init_struct.layer_default_blue = 0xFF; //该层显示范围外的颜色
    tli_layer_init_struct.layer_default_green = 0xFF;
    tli_layer_init_struct.layer_default_red = 0xFF;
    tli_layer_init_struct.layer_default_alpha = 0;
    /* TLI窗口混合配置 */
    tli_layer_init_struct.layer_acf1 = LAYER_ACF1_SA; //层混合模式 归一化的像素 Alpha 乘以归一化的恒定 Alpha
    tli_layer_init_struct.layer_acf2 = LAYER_ACF1_SA; //111：归一化的像素 Alpha 乘以归一化的恒定 Alpha
    /* TLI层帧缓冲基址配置 */
    /* 该成员应写入(一行像素数据占用的字节数+3)
    Line Lenth = 行有效像素个数 x 每个像素的字节数 + 3
    行有效像素个数 = LCD_PIXEL_WIDTH
    每个像素的字节数 = 2（RGB565/RGB1555）/ 3 (RGB888)/ 4（ARGB8888） */
    // tli_layer_init_struct.layer_frame_bufaddr = LCD_FRAME_BUFFER;     //缓存地址
    tli_layer_init_struct.layer_frame_bufaddr = (uint32_t) &gImage_background; //缓存地址
    tli_layer_init_struct.layer_frame_line_length = ((ACTIVE_WIDTH * 2) + 3);  //行长度 这个值为一行的字节数+3
    tli_layer_init_struct.layer_frame_buf_stride_offset = (ACTIVE_WIDTH * 2);  //步幅偏移 定义了从某行起始处到下一行起始处之间的字节数
    tli_layer_init_struct.layer_frame_total_line_number = ACTIVE_HEIGHT;       //总行数 定义了一帧行数
    tli_layer_init(LAYER0, &tli_layer_init_struct);
    //关闭抖动，有些颜色可能不在rgb565范围内，抖动功能打开以后就会去用其他颜色去逼近那个颜色，屏幕尺寸大同时像素密度又比较小的话，可能会有颗粒现象
    tli_dither_config(TLI_DITHER_DISABLE);
    /* 填充为白色 */
    // framebuffer_init(0xFFFF);

    tli_layer_enable(LAYER0);
    /* 重新加载配置 */
    tli_reload_config(TLI_REQUEST_RELOAD_EN);
    tli_enable();

//    /*配置第 2 层，若没有重写某个成员的值，则该成员使用跟第1层一样的配置 */
//    /* 配置本层的显存首地址，这里配置它紧挨在第1层的后面*/
//    tli_layer_init_struct.layer_frame_bufaddr = LCD_FRAME_BUFFER + BUFFER_OFFSET;
//
//    /* 配置混合因子，使用像素Alpha参与混合 */
//    tli_layer_init_struct.layer_acf1 = LAYER_ACF1_PASA;       //层混合模式 归一化的像素 Alpha 乘以归一化的恒定 Alpha
//    tli_layer_init_struct.layer_acf2 = LAYER_ACF2_PASA;       //111：归一化的像素 Alpha 乘以归一化的恒定 Alpha
//    /* 初始化第2层 */
//    tli_layer_init(LAYER1, &tli_layer_init_struct);
//    /* 填充为黄色 */
//    lcd_clear(LCD_COLOR565_YELLOW);
//     /*使能前景层 */
//    tli_layer_enable(LAYER1);
//    tli_reload_config(TLI_FRAME_BLANK_RELOAD_EN);

//    tli_reload_config(TLI_REQUEST_RELOAD_EN);
//    tli_enable();
}


void lcd_init() {
    my_tli_init();
    lcd_layer_init();

    gpio_bit_reset(LCD_SPI_RES_PORT, LCD_SPI_RES_PIN);
    GD_Delay_ms(20);
    gpio_bit_set(LCD_SPI_RES_PORT, LCD_SPI_RES_PIN);
    GD_Delay_ms(20);
    gpio_bit_reset(LCD_SPI_RES_PORT, LCD_SPI_RES_PIN);

    lcd_write_reg(0xFF, 0x30);
    return;
    lcd_write_reg(0xFF, 0x52);
    lcd_write_reg(0xFF, 0x01);
    lcd_write_reg(0xE3, 0x00);
//lcd_write_reg(0xF6,0xC0);
//lcd_write_reg(0xF0,0x0c);//bist
    lcd_write_reg(0x0A, 0x01);
    lcd_write_reg(0x23, 0xA0); //RGB MODE  A2
    lcd_write_reg(0x24, 0x10);
    lcd_write_reg(0x25, 0x09);
    lcd_write_reg(0x28, 0x47);
    lcd_write_reg(0x29, 0x01);
    lcd_write_reg(0x2A, 0xdf);
    lcd_write_reg(0x38, 0x7C); // 9C
    lcd_write_reg(0x39, 0x97); //A7
    lcd_write_reg(0x3A, 0x3f); //VCOM
    lcd_write_reg(0x91, 0x77); //57
    lcd_write_reg(0x92, 0x77); //57
    lcd_write_reg(0x99, 0x12);
    lcd_write_reg(0x9B, 0x1B);
    lcd_write_reg(0xA0, 0x55);
    lcd_write_reg(0xA1, 0x50);
    lcd_write_reg(0xA4, 0x9C);
    lcd_write_reg(0xA7, 0x02);
    lcd_write_reg(0xA8, 0x01);
    lcd_write_reg(0xA9, 0x01);
    lcd_write_reg(0xAA, 0xFC);
    lcd_write_reg(0xAB, 0x28);
    lcd_write_reg(0xAC, 0x06);
    lcd_write_reg(0xAD, 0x06);
    lcd_write_reg(0xAE, 0x06);
    lcd_write_reg(0xAF, 0x03);
    lcd_write_reg(0xB0, 0x08);
    lcd_write_reg(0xB1, 0x26);
    lcd_write_reg(0xB2, 0x28);
    lcd_write_reg(0xB3, 0x28);
    lcd_write_reg(0xB4, 0x03);
    lcd_write_reg(0xB5, 0x08);
    lcd_write_reg(0xB6, 0x26);
    lcd_write_reg(0xB7, 0x08);
    lcd_write_reg(0xB8, 0x26);
    lcd_write_reg(0xFF, 0x30);
    lcd_write_reg(0xFF, 0x52);
    lcd_write_reg(0xFF, 0x02);
    lcd_write_reg(0xB0, 0x02);
    lcd_write_reg(0xD0, 0x02);
    lcd_write_reg(0xB1, 0x0f);
    lcd_write_reg(0xD1, 0x10);
    lcd_write_reg(0xB2, 0x11);
    lcd_write_reg(0xD2, 0x12);
    lcd_write_reg(0xB3, 0x32);
    lcd_write_reg(0xD3, 0x33);
    lcd_write_reg(0xB4, 0x36);
    lcd_write_reg(0xD4, 0x36);
    lcd_write_reg(0xB5, 0x3C);
    lcd_write_reg(0xD5, 0x3C);
    lcd_write_reg(0xB6, 0x20);
    lcd_write_reg(0xD6, 0x20);
    lcd_write_reg(0xB7, 0x3e);
    lcd_write_reg(0xD7, 0x3e);
    lcd_write_reg(0xB8, 0x0E);
    lcd_write_reg(0xD8, 0x0d);
    lcd_write_reg(0xB9, 0x05);
    lcd_write_reg(0xD9, 0x05);
    lcd_write_reg(0xBA, 0x11);
    lcd_write_reg(0xDA, 0x12);
    lcd_write_reg(0xBB, 0x11);
    lcd_write_reg(0xDB, 0x11);
    lcd_write_reg(0xBC, 0x13);
    lcd_write_reg(0xDC, 0x14);
    lcd_write_reg(0xBD, 0x14);
    lcd_write_reg(0xDD, 0x14);
    lcd_write_reg(0xBE, 0x16);
    lcd_write_reg(0xDE, 0x18);
    lcd_write_reg(0xBF, 0x0e);
    lcd_write_reg(0xDF, 0x0f);
    lcd_write_reg(0xC0, 0x17);
    lcd_write_reg(0xE0, 0x17);
    lcd_write_reg(0xC1, 0x07);
    lcd_write_reg(0xE1, 0x08);
    lcd_write_reg(0xFF, 0x30);
    lcd_write_reg(0xFF, 0x52);
    lcd_write_reg(0xFF, 0x03);
    lcd_write_reg(0x07, 0x03);
    lcd_write_reg(0x08, 0x00);
    lcd_write_reg(0x09, 0x01);
    lcd_write_reg(0x30, 0x00);
    lcd_write_reg(0x31, 0x00);
    lcd_write_reg(0x32, 0x00);
    lcd_write_reg(0x33, 0x00);
    lcd_write_reg(0x34, 0x61);
    lcd_write_reg(0x35, 0xD4);
    lcd_write_reg(0x36, 0x24);
    lcd_write_reg(0x37, 0x03);
    lcd_write_reg(0x40, 0x02);
    lcd_write_reg(0x41, 0x03);
    lcd_write_reg(0x42, 0x04);
    lcd_write_reg(0x43, 0x05);
    lcd_write_reg(0x44, 0x11);
    lcd_write_reg(0x45, 0xE6);
    lcd_write_reg(0x46, 0xE7);
    lcd_write_reg(0x47, 0x11);
    lcd_write_reg(0x48, 0xE8);
    lcd_write_reg(0x49, 0xE9);
    lcd_write_reg(0x50, 0x06);
    lcd_write_reg(0x51, 0x07);
    lcd_write_reg(0x52, 0x08);
    lcd_write_reg(0x53, 0x09);
    lcd_write_reg(0x54, 0x11);
    lcd_write_reg(0x55, 0xEA);
    lcd_write_reg(0x56, 0xEB);
    lcd_write_reg(0x57, 0x11);
    lcd_write_reg(0x58, 0xEC);
    lcd_write_reg(0x59, 0xED);
    lcd_write_reg(0x82, 0x1E);
    lcd_write_reg(0x83, 0x1E);
    lcd_write_reg(0x84, 0x02);
    lcd_write_reg(0x85, 0x1E);
    lcd_write_reg(0x86, 0x1F);
    lcd_write_reg(0x87, 0x1E);
    lcd_write_reg(0x88, 0x1F);
    lcd_write_reg(0x89, 0x0E);
    lcd_write_reg(0x8A, 0x0E);
    lcd_write_reg(0x8B, 0x10);
    lcd_write_reg(0x8C, 0x10);
    lcd_write_reg(0x8D, 0x0A);
    lcd_write_reg(0x8E, 0x0A);
    lcd_write_reg(0x8F, 0x0C);
    lcd_write_reg(0x90, 0x0C);
    lcd_write_reg(0x98, 0x1E);
    lcd_write_reg(0x99, 0x1E);
    lcd_write_reg(0x9A, 0x01);
    lcd_write_reg(0x9B, 0x1E);
    lcd_write_reg(0x9C, 0x1F);
    lcd_write_reg(0x9D, 0x1E);
    lcd_write_reg(0x9E, 0x1F);
    lcd_write_reg(0x9F, 0x0D);
    lcd_write_reg(0xA0, 0x0D);
    lcd_write_reg(0xA1, 0x0F);
    lcd_write_reg(0xA2, 0x0F);
    lcd_write_reg(0xA3, 0x09);
    lcd_write_reg(0xA4, 0x09);
    lcd_write_reg(0xA5, 0x0B);
    lcd_write_reg(0xA6, 0x0B);
    lcd_write_reg(0xB2, 0x1F);
    lcd_write_reg(0xB3, 0x1E);
    lcd_write_reg(0xB4, 0x01);
    lcd_write_reg(0xB5, 0x1E);
    lcd_write_reg(0xB6, 0x1F);
    lcd_write_reg(0xB7, 0x1E);
    lcd_write_reg(0xB8, 0x1E);
    lcd_write_reg(0xB9, 0x0B);
    lcd_write_reg(0xBA, 0x0B);
    lcd_write_reg(0xBB, 0x09);
    lcd_write_reg(0xBC, 0x09);
    lcd_write_reg(0xBD, 0x0F);
    lcd_write_reg(0xBE, 0x0F);
    lcd_write_reg(0xBF, 0x0D);
    lcd_write_reg(0xC0, 0x0D);
    lcd_write_reg(0xC8, 0x1F);
    lcd_write_reg(0xC9, 0x1E);
    lcd_write_reg(0xCA, 0x02);
    lcd_write_reg(0xCB, 0x1E);
    lcd_write_reg(0xCC, 0x1F);
    lcd_write_reg(0xCD, 0x1E);
    lcd_write_reg(0xCE, 0x1E);
    lcd_write_reg(0xCF, 0x0C);
    lcd_write_reg(0xD0, 0x0C);
    lcd_write_reg(0xD1, 0x0A);
    lcd_write_reg(0xD2, 0x0A);
    lcd_write_reg(0xD3, 0x10);
    lcd_write_reg(0xD4, 0x10);
    lcd_write_reg(0xD5, 0x0E);
    lcd_write_reg(0xD6, 0x0E);
    lcd_write_reg(0xFF, 0x30);
    lcd_write_reg(0xFF, 0x52);
    lcd_write_reg(0xFF, 0x00);
    lcd_write_reg(0x3a, 0x77);
    lcd_write_reg(0x36, 0x0A); //反扫09
    lcd_write_reg(0x11, 0x00);
    GD_Delay_ms(200);
    lcd_write_reg(0x29, 0x00);
    GD_Delay_ms(100);
}
