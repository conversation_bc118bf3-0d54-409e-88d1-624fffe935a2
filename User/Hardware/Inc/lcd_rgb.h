//
// Created by <PERSON><PERSON> on 2025/6/23.
//

#ifndef Q800_GD_LCD_RGB_H
#define Q800_GD_LCD_RGB_H

#include "main.h"

// PE5 - LCD_BL
#define LCD_BL_GPIO_PORT   GPIOD
#define LCD_BL_GPIO_CLK    RCU_GPIOD
#define LCD_BL_GPIO_PIN    GPIO_PIN_0

// PG10 - LCD_SPI_SCS
#define LCD_SPI_SCS_PORT   GPIOG
#define LCD_SPI_SCS_CLK    RCU_GPIOG
#define LCD_SPI_SCS_PIN    GPIO_PIN_10

// PG11 - LCD_SPI_SCK
#define LCD_SPI_SCK_PORT   GPIOG
#define LCD_SPI_SCK_CLK    RCU_GPIOG
#define LCD_SPI_SCK_PIN    GPIO_PIN_11

// PG9 - LCD_SPI_SDA
#define LCD_SPI_SDA_PORT   GPIOG
#define LCD_SPI_SDA_CLK    RCU_GPIOG
#define LCD_SPI_SDA_PIN    GPIO_PIN_9

// PD7 - LCD_SPI_SDO
#define LCD_SPI_SDO_PORT   GPIOD
#define LCD_SPI_SDO_CLK    RCU_GPIOD
#define LCD_SPI_SDO_PIN    GPIO_PIN_7

// PD2 - LCD_SPI_RES
#define LCD_SPI_RES_PORT   GPIOD
#define LCD_SPI_RES_CLK    RCU_GPIOD
#define LCD_SPI_RES_PIN    GPIO_PIN_2

// LCD TLI GPIO 宏定义

// PG14 - TLI_B0
#define LCD_B0_GPIO_PORT   GPIOG
#define LCD_B0_GPIO_CLK    RCU_GPIOG
#define LCD_B0_GPIO_PIN    GPIO_PIN_14
#define LCD_B0_GPIO_AF     GPIO_AF_14

// PG12 - TLI_B1
#define LCD_B1_GPIO_PORT   GPIOG
#define LCD_B1_GPIO_CLK    RCU_GPIOG
#define LCD_B1_GPIO_PIN    GPIO_PIN_12
#define LCD_B1_GPIO_AF     GPIO_AF_14

// PC9 - TLI_B2
#define LCD_B2_GPIO_PORT   GPIOC
#define LCD_B2_GPIO_CLK    RCU_GPIOC
#define LCD_B2_GPIO_PIN    GPIO_PIN_9
#define LCD_B2_GPIO_AF     GPIO_AF_14

// PA8 - TLI_B3
#define LCD_B3_GPIO_PORT   GPIOA
#define LCD_B3_GPIO_CLK    RCU_GPIOA
#define LCD_B3_GPIO_PIN    GPIO_PIN_8
#define LCD_B3_GPIO_AF     GPIO_AF_13

// PC11 - TLI_B4
#define LCD_B4_GPIO_PORT   GPIOC
#define LCD_B4_GPIO_CLK    RCU_GPIOC
#define LCD_B4_GPIO_PIN    GPIO_PIN_11
#define LCD_B4_GPIO_AF     GPIO_AF_14

// PB5 - TLI_B5
#define LCD_B5_GPIO_PORT   GPIOB
#define LCD_B5_GPIO_CLK    RCU_GPIOB
#define LCD_B5_GPIO_PIN    GPIO_PIN_5
#define LCD_B5_GPIO_AF     GPIO_AF_3

// PB8 - TLI_B6
#define LCD_B6_GPIO_PORT   GPIOB
#define LCD_B6_GPIO_CLK    RCU_GPIOB
#define LCD_B6_GPIO_PIN    GPIO_PIN_8
#define LCD_B6_GPIO_AF     GPIO_AF_14

// PB9 - TLI_B7
#define LCD_B7_GPIO_PORT   GPIOB
#define LCD_B7_GPIO_CLK    RCU_GPIOB
#define LCD_B7_GPIO_PIN    GPIO_PIN_9
#define LCD_B7_GPIO_AF     GPIO_AF_14

// PB1 - TLI_G0
#define LCD_G0_GPIO_PORT   GPIOB
#define LCD_G0_GPIO_CLK    RCU_GPIOB
#define LCD_G0_GPIO_PIN    GPIO_PIN_1
#define LCD_G0_GPIO_AF     GPIO_AF_14

// PB0 - TLI_G1
#define LCD_G1_GPIO_PORT   GPIOB
#define LCD_G1_GPIO_CLK    RCU_GPIOB
#define LCD_G1_GPIO_PIN    GPIO_PIN_0
#define LCD_G1_GPIO_AF     GPIO_AF_14

// PA6 - TLI_G2
#define LCD_G2_GPIO_PORT   GPIOA
#define LCD_G2_GPIO_CLK    RCU_GPIOA
#define LCD_G2_GPIO_PIN    GPIO_PIN_6
#define LCD_G2_GPIO_AF     GPIO_AF_14

// PE11 - TLI_G3
#define LCD_G3_GPIO_PORT   GPIOE
#define LCD_G3_GPIO_CLK    RCU_GPIOE
#define LCD_G3_GPIO_PIN    GPIO_PIN_11
#define LCD_G3_GPIO_AF     GPIO_AF_14

// PB10 - TLI_G4
#define LCD_G4_GPIO_PORT   GPIOB
#define LCD_G4_GPIO_CLK    RCU_GPIOB
#define LCD_G4_GPIO_PIN    GPIO_PIN_10
#define LCD_G4_GPIO_AF     GPIO_AF_14

// PC1 - TLI_G5
#define LCD_G5_GPIO_PORT   GPIOC
#define LCD_G5_GPIO_CLK    RCU_GPIOC
#define LCD_G5_GPIO_PIN    GPIO_PIN_1
#define LCD_G5_GPIO_AF     GPIO_AF_14

// PC7 - TLI_G6
#define LCD_G6_GPIO_PORT   GPIOC
#define LCD_G6_GPIO_CLK    RCU_GPIOC
#define LCD_G6_GPIO_PIN    GPIO_PIN_7
#define LCD_G6_GPIO_AF     GPIO_AF_14

// PD3 - TLI_G7
#define LCD_G7_GPIO_PORT   GPIOD
#define LCD_G7_GPIO_CLK    RCU_GPIOD
#define LCD_G7_GPIO_PIN    GPIO_PIN_3
#define LCD_G7_GPIO_AF     GPIO_AF_14

// PG13 - TLI_R0
#define LCD_R0_GPIO_PORT   GPIOG
#define LCD_R0_GPIO_CLK    RCU_GPIOG
#define LCD_R0_GPIO_PIN    GPIO_PIN_13
#define LCD_R0_GPIO_AF     GPIO_AF_14

// PA2 - TLI_R1
#define LCD_R1_GPIO_PORT   GPIOA
#define LCD_R1_GPIO_CLK    RCU_GPIOA
#define LCD_R1_GPIO_PIN    GPIO_PIN_2
#define LCD_R1_GPIO_AF     GPIO_AF_14

// PC10 - TLI_R2
#define LCD_R2_GPIO_PORT   GPIOC
#define LCD_R2_GPIO_CLK    RCU_GPIOC
#define LCD_R2_GPIO_PIN    GPIO_PIN_10
#define LCD_R2_GPIO_AF     GPIO_AF_14

// PA15 - TLI_R3
#define LCD_R3_GPIO_PORT   GPIOA
#define LCD_R3_GPIO_CLK    RCU_GPIOA
#define LCD_R3_GPIO_PIN    GPIO_PIN_15
#define LCD_R3_GPIO_AF     GPIO_AF_9

// PA5 - TLI_R4
#define LCD_R4_GPIO_PORT   GPIOA
#define LCD_R4_GPIO_CLK    RCU_GPIOA
#define LCD_R4_GPIO_PIN    GPIO_PIN_5
#define LCD_R4_GPIO_AF     GPIO_AF_14

// PC0 - TLI_R5
#define LCD_R5_GPIO_PORT   GPIOC
#define LCD_R5_GPIO_CLK    RCU_GPIOC
#define LCD_R5_GPIO_PIN    GPIO_PIN_0
#define LCD_R5_GPIO_AF     GPIO_AF_14

// PC12 - TLI_R6
#define LCD_R6_GPIO_PORT   GPIOC
#define LCD_R6_GPIO_CLK    RCU_GPIOC
#define LCD_R6_GPIO_PIN    GPIO_PIN_12
#define LCD_R6_GPIO_AF     GPIO_AF_14

// PC4 - TLI_R7
#define LCD_R7_GPIO_PORT   GPIOC
#define LCD_R7_GPIO_CLK    RCU_GPIOC
#define LCD_R7_GPIO_PIN    GPIO_PIN_4
#define LCD_R7_GPIO_AF     GPIO_AF_14

// PE13 - TLI_DE
#define LCD_DE_GPIO_PORT   GPIOE
#define LCD_DE_GPIO_CLK    RCU_GPIOE
#define LCD_DE_GPIO_PIN    GPIO_PIN_13
#define LCD_DE_GPIO_AF     GPIO_AF_14

// PA4 - TLI_VSYNC
#define LCD_V_GPIO_PORT   GPIOA
#define LCD_V_GPIO_CLK    RCU_GPIOA
#define LCD_V_GPIO_PIN    GPIO_PIN_4
#define LCD_V_GPIO_AF     GPIO_AF_14

// PE15 - TLI_HSYNC
#define LCD_H_GPIO_PORT   GPIOE
#define LCD_H_GPIO_CLK    RCU_GPIOE
#define LCD_H_GPIO_PIN    GPIO_PIN_15
#define LCD_H_GPIO_AF     GPIO_AF_10

// PE14 - TLI_PIXCLK
#define LCD_PCLK_GPIO_PORT   GPIOE
#define LCD_PCLK_GPIO_CLK    RCU_GPIOE
#define LCD_PCLK_GPIO_PIN    GPIO_PIN_14
#define LCD_PCLK_GPIO_AF     GPIO_AF_14

void lcd_init();

void lcd_gpio_init();

#endif //Q800_GD_LCD_RGB_H
