/*!
    \file    gd32h7xx_libopt.h
    \brief   library optional for gd32h7xx

    \version 2025-01-24, V1.4.0, firmware for GD32H7xx
*/

/*
    Copyright (c) 2025, GigaDevice Semiconductor Inc.

    Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice,
       this list of conditions and the following disclaimer in the documentation
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors
       may be used to endorse or promote products derived from this software without
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
*/

#ifndef GD32H7XX_LIBOPT_H
#define GD32H7XX_LIBOPT_H

#include "gd32h7xx_adc.h"
#include "gd32h7xx_axiim.h"
#include "gd32h7xx_can.h"
#include "gd32h7xx_cau.h"
#include "gd32h7xx_cmp.h"
#include "gd32h7xx_cpdm.h"
#include "gd32h7xx_crc.h"
#include "gd32h7xx_ctc.h"
#include "gd32h7xx_dac.h"
#include "gd32h7xx_dbg.h"
#include "gd32h7xx_dci.h"
#include "gd32h7xx_dma.h"
#include "gd32h7xx_edout.h"
#include "gd32h7xx_efuse.h"
#include "gd32h7xx_enet.h"
#include "gd32h7xx_exmc.h"
#include "gd32h7xx_exti.h"
#include "gd32h7xx_fac.h"
#include "gd32h7xx_fmc.h"
#include "gd32h7xx_fwdgt.h"
#include "gd32h7xx_gpio.h"
#include "gd32h7xx_hau.h"
#include "gd32h7xx_hpdf.h"
#include "gd32h7xx_hwsem.h"
#include "gd32h7xx_i2c.h"
#include "gd32h7xx_ipa.h"
#include "gd32h7xx_lpdts.h"
#include "gd32h7xx_mdio.h"
#include "gd32h7xx_mdma.h"
#include "gd32h7xx_misc.h"
#include "gd32h7xx_ospi.h"
#include "gd32h7xx_ospim.h"
#include "gd32h7xx_pmu.h"
#include "gd32h7xx_rameccmu.h"
#include "gd32h7xx_rcu.h"
#include "gd32h7xx_rspdif.h"
#include "gd32h7xx_rtc.h"
#include "gd32h7xx_rtdec.h"
#include "gd32h7xx_sai.h"
#include "gd32h7xx_sdio.h"
#include "gd32h7xx_spi.h"
#include "gd32h7xx_syscfg.h"
#include "gd32h7xx_timer.h"
#include "gd32h7xx_tli.h"
#include "gd32h7xx_tmu.h"
#include "gd32h7xx_trigsel.h"
#include "gd32h7xx_trng.h"
#include "gd32h7xx_usart.h"
#include "gd32h7xx_vref.h"
#include "gd32h7xx_wwdgt.h"

#endif /* GD32H7XX_LIBOPT_H */
